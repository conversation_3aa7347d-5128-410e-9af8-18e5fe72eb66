package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2022/1/7.
 * description：炬联
 */
public class BaseEntity {
    @SerializedName("downUrl")
    private String downUrl;
    @SerializedName("success")
    private Integer success;
    @SerializedName("sucess")
    private Integer sucess;
    @SerializedName("message")
    private String message;
    @SerializedName("msg")
    private String msg;
    @SerializedName("code")
    private String code;
    @SerializedName("isName")
    private String isName;

    @SerializedName("isReal")
    private String isReal;

    public String getIsName() {
        return isName;
    }

    public void setIsName(String isName) {
        this.isName = isName;
    }

    public String getIsReal() {
        return isReal;
    }

    public void setIsReal(String isReal) {
        this.isReal = isReal;
    }

    public Integer getSucess() {
        return sucess;
    }

    public void setSucess(Integer sucess) {
        this.sucess = sucess;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getSuccess() {
        return success;
    }

    public void setSuccess(Integer success) {
        this.success = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDownUrl() {
        return downUrl;
    }

    public void setDownUrl(String downUrl) {
        this.downUrl = downUrl;
    }


    @Override
    public String toString() {
        return "BaseEntity{" +
                "downUrl='" + downUrl + '\'' +
                ", success=" + success +
                ", msg='" + msg + '\'' +
                ", code='" + code + '\'' +
                '}';
    }
}
