package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/24 14:40
 * @description:
 */
public class LabelInfo {

    @SerializedName("text")
    private String text;

    @SerializedName("value")
    private String value;

    @SerializedName("maxSelectNum")
    private String maxSelectNum;
    @SerializedName("labelList")
    private List<String> labelList;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getMaxSelectNum() {
        return maxSelectNum;
    }

    public void setMaxSelectNum(String maxSelectNum) {
        this.maxSelectNum = maxSelectNum;
    }

    public List<String> getLabelList() {
        return labelList;
    }

    public void setLabelList(List<String> labelList) {
        this.labelList = labelList;
    }
}
