package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;

/**
 * <AUTHOR>
 * @date 2023/11/28 11:37
 * @description:
 */

@Entity
public class UserInfo {
    @SerializedName("id")
    private String id;
    @SerializedName("phone")
    private String phone;
    @SerializedName("avatar")
    private String avatar;
    @SerializedName("nickname")
    private String nickname;
    @SerializedName("sex")
    private String sex;

    @SerializedName("invitationCode")
    private int invitationCode;

    @Generated(hash = 1009250413)
    public UserInfo(String id, String phone, String avatar, String nickname,
                    String sex, int invitationCode) {
        this.id = id;
        this.phone = phone;
        this.avatar = avatar;
        this.nickname = nickname;
        this.sex = sex;
        this.invitationCode = invitationCode;
    }

    @Generated(hash = 1279772520)
    public UserInfo() {
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAvatar() {
        return this.avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return this.nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSex() {
        return this.sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public int getInvitationCode() {
        return this.invitationCode;
    }

    public void setInvitationCode(int invitationCode) {
        this.invitationCode = invitationCode;
    }
}
