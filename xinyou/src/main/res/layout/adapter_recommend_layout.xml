<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@android:color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp">

        <!-- 用户头像区域 -->
        <RelativeLayout
            android:id="@+id/user_layout"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:layout_centerHorizontal="true">

            <!-- 背景头像 -->
            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/user_riv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@mipmap/ic_launcher_app"
                app:riv_corner_radius="8dp" />

            <!-- 头像框 -->
            <ImageView
                android:id="@+id/iv_txk"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <!-- 视频播放区域 -->
            <FrameLayout
                android:id="@+id/video_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.makeramen.roundedimageview.RoundedImageView
                        android:id="@+id/image_view"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        app:riv_corner_radius="8dp"
                        tools:background="@color/gray"/>
                <ImageView
                    android:id="@+id/iv_player"
                    android:layout_width="@dimen/dp_20"
                    android:layout_height="@dimen/dp_20"
                    android:layout_gravity="center"
                    android:src="@mipmap/player" />

            </FrameLayout>

            <!-- 在线状态指示器 -->
            <LinearLayout
                android:id="@+id/online_status_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_alignParentTop="true"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:background="@drawable/bg_online_status"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp">

                <View
                    android:id="@+id/online_view"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:background="@drawable/bg_online_dot" />

                <TextView
                    android:id="@+id/online_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="在线"
                    android:textColor="@android:color/white"
                    android:textSize="10sp" />

            </LinearLayout>

            <!-- 视频通话和聊天按钮 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="8dp"
                android:orientation="horizontal"/>

        </RelativeLayout>

        <!-- 用户信息区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/user_layout"
            android:layout_marginTop="12dp"
            android:orientation="vertical">

            <!-- 用户名和认证信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/user_name_tv"
                        android:layout_width="104dp"
                        android:layout_height="wrap_content"
                        android:text="柠檬姐姐"
                        android:textColor="@color/color_333333"
                        android:textSize="20sp"
                        android:textStyle="bold"/>

                <ImageView
                    android:id="@+id/user_isname_tv"
                    android:layout_width="90dp"
                    android:layout_height="30dp"
                    android:src="@mipmap/realhuman"
                    android:scaleType="fitXY"
                    android:paddingStart="@dimen/dp_5"
                    android:paddingEnd="@dimen/dp_5" />

                <ImageView
                    android:id="@+id/vip_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp_20"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>

            <!-- 用户基本信息 -->

            <!-- 用户标签和打招呼按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                        android:id="@+id/accost_button"
                        android:layout_width="match_parent"
                        android:layout_height="47dp"
                        android:src="@mipmap/go_chat"
                        android:paddingHorizontal="16dp"
                        android:textColor="@android:color/white"/>
            </LinearLayout>

            <!-- 隐藏的朋友圈文本 -->
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/frient_xuan_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:textColor="@color/color_7E7E7E"
                android:textSize="@dimen/sp_12"
                android:visibility="gone" />

        </LinearLayout>

    </RelativeLayout>

</androidx.cardview.widget.CardView>
